import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_explore_data():
    """加载并探索数据结构"""
    print("正在加载数据...")

    # 尝试读取Excel文件的所有sheet
    try:
        excel_file = pd.ExcelFile('data.xlsx')
        print(f"Excel文件包含的工作表: {excel_file.sheet_names}")

        # 读取第一个工作表
        df = pd.read_excel('data.xlsx', sheet_name=0)
        print(f"\n数据形状: {df.shape}")
        print(f"\n列名: {list(df.columns)}")
        print(f"\n前5行数据:")
        print(df.head())

        print(f"\n数据类型:")
        print(df.dtypes)

        print(f"\n缺失值统计:")
        print(df.isnull().sum())

        return df

    except Exception as e:
        print(f"读取数据时出错: {e}")
        return None

def identify_choice_experiment_structure(df):
    """识别选择实验的结构"""
    print("\n" + "="*50)
    print("识别选择实验结构")
    print("="*50)

    # 寻找选择题（Q8-Q15）
    choice_questions = [col for col in df.columns if col.startswith('Q') and
                       'Imagine you are in a store' in col and 'Option A, Option B or Option C' in col]

    print(f"发现 {len(choice_questions)} 个选择题:")
    for i, q in enumerate(choice_questions, 8):
        print(f"Q{i}: 选择题")

    # 分析选择题的回答
    choice_data = {}
    for q in choice_questions:
        if q in df.columns:
            choices = df[q].value_counts()
            print(f"\n{q}的选择分布:")
            print(choices)
            choice_data[q] = choices

    return choice_questions, choice_data

def identify_treatment_groups(df):
    """识别处理组"""
    print("\n" + "="*50)
    print("识别处理组")
    print("="*50)

    # 查看Q7 (Cheap talk)，这可能是处理组的指示
    if 'Q7. Cheap talk (CT)\nBefore proceeding to the next set of questions, we want to ask for your feedback about the responses you provided so far. It is vital to our study that we only include responses from participants who devoted their full attention to this study. In your honest opinion, should we use your responses, or should we discard your responses since you did not devote your full attention to the questions so far? ' in df.columns:
        q7_col = 'Q7. Cheap talk (CT)\nBefore proceeding to the next set of questions, we want to ask for your feedback about the responses you provided so far. It is vital to our study that we only include responses from participants who devoted their full attention to the questions so far. In your honest opinion, should we use your responses, or should we discard your responses since you did not devote your full attention to the questions so far? '
        print("Q7 (Cheap talk) 分布:")
        print(df[q7_col].value_counts())

    # 寻找可能的处理组变量
    # 通常处理组信息可能在UserID、UserNo或其他变量中编码
    print("\nUserID 分布（前10个）:")
    print(df['UserID'].value_counts().head(10))

    # 检查是否有明显的分组模式
    print(f"\n总样本数: {len(df)}")
    print(f"唯一UserID数: {df['UserID'].nunique()}")

    return None

def analyze_basic_stats(df):
    """基本统计分析"""
    print("\n" + "="*50)
    print("基本统计分析")
    print("="*50)
    
    # 数值型变量的描述性统计
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    if len(numeric_cols) > 0:
        print("\n数值型变量描述性统计:")
        print(df[numeric_cols].describe())
    
    # 分类变量的频数统计
    categorical_cols = df.select_dtypes(include=['object']).columns
    if len(categorical_cols) > 0:
        print("\n分类变量频数统计:")
        for col in categorical_cols[:5]:  # 只显示前5个分类变量
            print(f"\n{col}:")
            print(df[col].value_counts().head())

def identify_key_variables(df):
    """识别关键变量"""
    print("\n" + "="*50)
    print("识别关键变量")
    print("="*50)
    
    columns = df.columns.tolist()
    
    # 寻找可能的关键变量
    treatment_vars = [col for col in columns if any(keyword in col.lower() for keyword in ['treatment', 'group', 'condition', 'info'])]
    meat_vars = [col for col in columns if any(keyword in col.lower() for keyword in ['beef', 'meat', 'lab', 'plant', 'cultured', 'pork'])]
    price_vars = [col for col in columns if any(keyword in col.lower() for keyword in ['price', 'cost', 'wtp', 'willingness'])]
    choice_vars = [col for col in columns if any(keyword in col.lower() for keyword in ['choice', 'select', 'prefer'])]
    demo_vars = [col for col in columns if any(keyword in col.lower() for keyword in ['age', 'gender', 'income', 'education', 'vegetarian'])]
    
    print(f"可能的处理组变量: {treatment_vars}")
    print(f"可能的肉类相关变量: {meat_vars}")
    print(f"可能的价格相关变量: {price_vars}")
    print(f"可能的选择相关变量: {choice_vars}")
    print(f"可能的人口统计变量: {demo_vars}")
    
    return {
        'treatment': treatment_vars,
        'meat': meat_vars,
        'price': price_vars,
        'choice': choice_vars,
        'demographics': demo_vars
    }

def analyze_demographics(df):
    """分析人口统计变量"""
    print("\n" + "="*50)
    print("人口统计分析")
    print("="*50)

    # 年龄分析
    age_col = 'Q4. What is your age?'
    if age_col in df.columns:
        print("年龄分布:")
        print(df[age_col].value_counts().sort_index())

    # 性别分析
    gender_col = 'Q5. What is your gender?'
    if gender_col in df.columns:
        print("\n性别分布:")
        print(df[gender_col].value_counts())

    # 教育背景
    edu_col = 'Q31.  What is your educational background? (Mark the box next to the highest level of education you have completed)'
    if edu_col in df.columns:
        print("\n教育背景分布:")
        print(df[edu_col].value_counts())

    # 收入
    income_col = 'Q36. Please indicate your approximate annual household income before taxes: '
    if income_col in df.columns:
        print("\n收入分布:")
        print(df[income_col].value_counts())

def create_choice_experiment_dataset(df):
    """创建选择实验数据集"""
    print("\n" + "="*50)
    print("创建选择实验数据集")
    print("="*50)

    # 获取选择题列
    choice_cols = [col for col in df.columns if col.startswith('Q') and
                   'Imagine you are in a store' in col and 'Option A, Option B or Option C' in col]

    # 创建长格式数据
    choice_data = []

    for _, row in df.iterrows():
        user_id = row['UserID']

        # 人口统计变量
        age = row.get('Q4. What is your age?', np.nan)
        gender = row.get('Q5. What is your gender?', np.nan)
        education = row.get('Q31.  What is your educational background? (Mark the box next to the highest level of education you have completed)', np.nan)
        income = row.get('Q36. Please indicate your approximate annual household income before taxes: ', np.nan)

        # 对每个选择题创建记录
        for i, choice_col in enumerate(choice_cols, 1):
            choice = row.get(choice_col, np.nan)

            choice_data.append({
                'user_id': user_id,
                'choice_set': i,
                'choice': choice,
                'age': age,
                'gender': gender,
                'education': education,
                'income': income
            })

    choice_df = pd.DataFrame(choice_data)
    print(f"创建的选择数据集形状: {choice_df.shape}")
    print(f"选择分布:")
    print(choice_df['choice'].value_counts())

    return choice_df

def main():
    """主函数"""
    print("开始分析数据...")

    # 加载数据
    df = load_and_explore_data()
    if df is None:
        return

    # 基本统计分析
    analyze_basic_stats(df)

    # 识别关键变量
    key_vars = identify_key_variables(df)

    # 识别选择实验结构
    choice_questions, _ = identify_choice_experiment_structure(df)

    # 识别处理组
    identify_treatment_groups(df)

    # 分析人口统计
    analyze_demographics(df)

    # 创建选择实验数据集
    choice_df = create_choice_experiment_dataset(df)

    # 保存数据
    choice_df.to_csv('choice_experiment_data.csv', index=False, encoding='utf-8')
    print("\n选择实验数据已保存到 choice_experiment_data.csv")

    # 保存数据概览
    with open('data_overview.txt', 'w', encoding='utf-8') as f:
        f.write("数据概览报告\n")
        f.write("="*50 + "\n")
        f.write(f"数据形状: {df.shape}\n")
        f.write(f"列名: {list(df.columns)}\n\n")

        f.write("关键变量识别:\n")
        for key, vars_list in key_vars.items():
            f.write(f"{key}: {vars_list}\n")

        f.write(f"\n选择题数量: {len(choice_questions)}\n")
        f.write(f"选择实验数据集形状: {choice_df.shape}\n")

    print("\n数据概览已保存到 data_overview.txt")
    return df, choice_df

if __name__ == "__main__":
    df, choice_df = main()
